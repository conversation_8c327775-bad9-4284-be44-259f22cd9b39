import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/themes/merchant_theme.dart';

/// Professional Sales Representatives Management Screen
/// Features: Sales rep listing, performance tracking, commission management,
/// responsive design, and modern UI components
class SalesRepsScreen extends ConsumerStatefulWidget {
  const SalesRepsScreen({super.key});

  @override
  ConsumerState<SalesRepsScreen> createState() => _SalesRepsScreenState();
}

class _SalesRepsScreenState extends ConsumerState<SalesRepsScreen>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedStatus = 'All';
  String _selectedPerformance = 'All';
  
  late AnimationController _pageController;
  late Animation<double> _pageAnimation;
  
  final List<String> _statusFilters = [
    'All',
    'Active',
    'Inactive',
    'On Leave',
    'Terminated',
  ];
  
  final List<String> _performanceFilters = [
    'All',
    'Top Performers',
    'Average',
    'Below Average',
    'New',
  ];

  // Mock data for demonstration
  final List<Map<String, dynamic>> _salesReps = [
    {
      'id': 'SR001',
      'name': '<PERSON> <PERSON>',
      'email': '<EMAIL>',
      'phone': '+234 ************',
      'status': 'Active',
      'performance': 'Top Performer',
      'commission': 15.0,
      'salesThisMonth': 1250000,
      'salesLastMonth': 980000,
      'totalSales': 8500000,
      'customers': 45,
      'joinDate': '2023-01-15',
      'region': 'Lagos Central',
      'specialization': 'Electronics',
    },
    {
      'id': 'SR002',
      'name': 'Sarah Johnson',
      'email': '<EMAIL>',
      'phone': '+234 ************',
      'status': 'Active',
      'performance': 'Average',
      'commission': 12.0,
      'salesThisMonth': 850000,
      'salesLastMonth': 920000,
      'totalSales': 6200000,
      'customers': 32,
      'joinDate': '2023-03-20',
      'region': 'Lagos West',
      'specialization': 'Fashion',
    },
    {
      'id': 'SR003',
      'name': 'Michael Brown',
      'email': '<EMAIL>',
      'phone': '+234 ************',
      'status': 'Active',
      'performance': 'Top Performer',
      'commission': 18.0,
      'salesThisMonth': 1500000,
      'salesLastMonth': 1350000,
      'totalSales': 12000000,
      'customers': 58,
      'joinDate': '2022-11-10',
      'region': 'Lagos East',
      'specialization': 'Home & Garden',
    },
    {
      'id': 'SR004',
      'name': 'Emily Davis',
      'email': '<EMAIL>',
      'phone': '+234 ************',
      'status': 'On Leave',
      'performance': 'Average',
      'commission': 10.0,
      'salesThisMonth': 0,
      'salesLastMonth': 750000,
      'totalSales': 4500000,
      'customers': 28,
      'joinDate': '2023-05-12',
      'region': 'Lagos North',
      'specialization': 'Beauty & Health',
    },
    {
      'id': 'SR005',
      'name': 'David Wilson',
      'email': '<EMAIL>',
      'phone': '+234 ************',
      'status': 'Active',
      'performance': 'New',
      'commission': 8.0,
      'salesThisMonth': 450000,
      'salesLastMonth': 0,
      'totalSales': 450000,
      'customers': 12,
      'joinDate': '2024-01-05',
      'region': 'Lagos South',
      'specialization': 'Sports & Outdoors',
    },
  ];

  @override
  void initState() {
    super.initState();
    _pageController = AnimationController(
      duration: MerchantTheme.slowAnimation,
      vsync: this,
    );
    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeOutCubic,
    );
    _pageController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final padding = MerchantTheme.getResponsivePadding(context);
    
    return Theme(
      data: MerchantTheme.lightTheme,
      child: Scaffold(
        backgroundColor: MerchantTheme.neutral50,
        body: FadeTransition(
          opacity: _pageAnimation,
          child: CustomScrollView(
            slivers: [
              // Header
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(padding, padding * 2, padding, padding),
                  decoration: const BoxDecoration(
                    gradient: MerchantTheme.primaryGradient,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.people, color: Colors.white, size: 32),
                          const SizedBox(width: MerchantTheme.spacing16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Sales Representatives',
                                  style: MerchantTheme.headline2.copyWith(color: Colors.white),
                                ),
                                Text(
                                  'Manage your sales team and track performance',
                                  style: MerchantTheme.bodyLarge.copyWith(color: Colors.white70),
                                ),
                              ],
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _showAddSalesRepDialog,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: MerchantTheme.primaryBlue,
                            ),
                            icon: const Icon(Icons.add),
                            label: const Text('Add Sales Rep'),
                          ),
                        ],
                      ),
                      const SizedBox(height: MerchantTheme.spacing24),
                      _buildSalesRepStats(),
                    ],
                  ),
                ),
              ),
              
              // Search and Filters
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(padding),
                  child: _buildSearchAndFilters(),
                ),
              ),
              
              // Sales Reps List
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: padding),
                  child: _buildSalesRepsSection(),
                ),
              ),
              
              const SliverToBoxAdapter(
                child: SizedBox(height: MerchantTheme.spacing64),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSalesRepStats() {
    final totalReps = _salesReps.length;
    final activeReps = _salesReps.where((rep) => rep['status'] == 'Active').length;
    final topPerformers = _salesReps.where((rep) => rep['performance'] == 'Top Performer').length;
    final totalSales = _salesReps.fold<double>(0, (sum, rep) => sum + rep['salesThisMonth']);

    return Row(
      children: [
        Expanded(child: _buildStatCard('Total Reps', totalReps.toString(), Icons.people, MerchantTheme.primaryBlue)),
        const SizedBox(width: MerchantTheme.spacing16),
        Expanded(child: _buildStatCard('Active', activeReps.toString(), Icons.check_circle, MerchantTheme.successGreen)),
        const SizedBox(width: MerchantTheme.spacing16),
        Expanded(child: _buildStatCard('Top Performers', topPerformers.toString(), Icons.star, MerchantTheme.warningOrange)),
        const SizedBox(width: MerchantTheme.spacing16),
        Expanded(child: _buildStatCard('Monthly Sales', '₦${(totalSales / 1000000).toStringAsFixed(1)}M', Icons.attach_money, MerchantTheme.secondaryBlue)),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Text(
                value,
                style: MerchantTheme.headline3.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          Text(
            title,
            style: MerchantTheme.bodyMedium.copyWith(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(MerchantTheme.radiusLarge),
            boxShadow: MerchantTheme.cardShadow,
          ),
          child: TextField(
            controller: _searchController,
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: 'Search sales reps by name, email, or ID...',
              prefixIcon: const Icon(Icons.search, color: MerchantTheme.neutral500),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: MerchantTheme.neutral500),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: MerchantTheme.spacing20,
                vertical: MerchantTheme.spacing16,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: MerchantTheme.spacing16),
        
        Row(
          children: [
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedStatus,
                items: _statusFilters,
                onChanged: (value) => setState(() => _selectedStatus = value!),
                label: 'Status',
              ),
            ),
            const SizedBox(width: MerchantTheme.spacing12),
            Expanded(
              child: _buildFilterDropdown(
                value: _selectedPerformance,
                items: _performanceFilters,
                onChanged: (value) => setState(() => _selectedPerformance = value!),
                label: 'Performance',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterDropdown({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: MerchantTheme.spacing16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        border: Border.all(color: MerchantTheme.neutral200),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          items: items.map((item) => DropdownMenuItem(
            value: item,
            child: Text(item, style: MerchantTheme.bodyMedium),
          )).toList(),
          onChanged: onChanged,
          icon: const Icon(Icons.keyboard_arrow_down, color: MerchantTheme.neutral500),
          isExpanded: true,
        ),
      ),
    );
  }

  Widget _buildSalesRepsSection() {
    final filteredReps = _filterSalesReps();
    
    if (filteredReps.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: filteredReps.length,
      itemBuilder: (context, index) {
        final rep = filteredReps[index];
        return Container(
          margin: const EdgeInsets.only(bottom: MerchantTheme.spacing16),
          child: _buildSalesRepCard(rep),
        );
      },
    );
  }

  List<Map<String, dynamic>> _filterSalesReps() {
    return _salesReps.where((rep) {
      // Search filter
      final matchesSearch = _searchQuery.isEmpty ||
          rep['name'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          rep['email'].toString().toLowerCase().contains(_searchQuery.toLowerCase()) ||
          rep['id'].toString().toLowerCase().contains(_searchQuery.toLowerCase());
      
      // Status filter
      final matchesStatus = _selectedStatus == 'All' || rep['status'] == _selectedStatus;
      
      // Performance filter
      final matchesPerformance = _selectedPerformance == 'All' || rep['performance'] == _selectedPerformance;
      
      return matchesSearch && matchesStatus && matchesPerformance;
    }).toList();
  }

  Widget _buildSalesRepCard(Map<String, dynamic> rep) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: ExpansionTile(
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: _getStatusColor(rep['status']).withOpacity(0.1),
          child: Text(
            rep['name'].toString().substring(0, 2).toUpperCase(),
            style: MerchantTheme.headline4.copyWith(
              color: _getStatusColor(rep['status']),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          rep['name'],
          style: MerchantTheme.headline4,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: MerchantTheme.spacing8),
            Text(
              rep['email'],
              style: MerchantTheme.bodyMedium.copyWith(
                color: MerchantTheme.neutral600,
              ),
            ),
            const SizedBox(height: MerchantTheme.spacing4),
            Text(
              rep['phone'],
              style: MerchantTheme.bodySmall.copyWith(
                color: MerchantTheme.neutral500,
              ),
            ),
            const SizedBox(height: MerchantTheme.spacing8),
            Row(
              children: [
                _buildStatusChip(rep['status']),
                const SizedBox(width: MerchantTheme.spacing8),
                _buildPerformanceChip(rep['performance']),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '₦${(rep['salesThisMonth'] / 1000).toStringAsFixed(0)}K',
              style: MerchantTheme.headline5.copyWith(
                color: MerchantTheme.successGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'This Month',
              style: MerchantTheme.labelSmall.copyWith(
                color: MerchantTheme.neutral500,
              ),
            ),
            const SizedBox(height: MerchantTheme.spacing4),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'view':
                    _viewSalesRepDetails(rep);
                    break;
                  case 'edit':
                    _editSalesRep(rep);
                    break;
                  case 'performance':
                    _viewPerformanceReport(rep);
                    break;
                  case 'commission':
                    _manageCommission(rep);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: Row(
                    children: [
                      Icon(Icons.visibility, size: 20),
                      SizedBox(width: 8),
                      Text('View Details'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 20),
                      SizedBox(width: 8),
                      Text('Edit Rep'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'performance',
                  child: Row(
                    children: [
                      Icon(Icons.analytics, size: 20),
                      SizedBox(width: 8),
                      Text('Performance Report'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'commission',
                  child: Row(
                    children: [
                      Icon(Icons.attach_money, size: 20),
                      SizedBox(width: 8),
                      Text('Manage Commission'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(MerchantTheme.spacing16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(),
                const SizedBox(height: MerchantTheme.spacing12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSalesRepDetail('Region', rep['region']),
                    ),
                    Expanded(
                      child: _buildSalesRepDetail('Specialization', rep['specialization']),
                    ),
                  ],
                ),
                const SizedBox(height: MerchantTheme.spacing12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSalesRepDetail('Commission', '${rep['commission']}%'),
                    ),
                    Expanded(
                      child: _buildSalesRepDetail('Customers', rep['customers'].toString()),
                    ),
                  ],
                ),
                const SizedBox(height: MerchantTheme.spacing12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSalesRepDetail('Join Date', rep['joinDate']),
                    ),
                    Expanded(
                      child: _buildSalesRepDetail('Total Sales', '₦${(rep['totalSales'] / 1000000).toStringAsFixed(1)}M'),
                    ),
                  ],
                ),
                const SizedBox(height: MerchantTheme.spacing16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _viewSalesRepDetails(rep),
                        child: const Text('View Details'),
                      ),
                    ),
                    const SizedBox(width: MerchantTheme.spacing12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _viewPerformanceReport(rep),
                        child: const Text('Performance'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesRepDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: MerchantTheme.labelMedium.copyWith(
            color: MerchantTheme.neutral500,
          ),
        ),
        const SizedBox(height: MerchantTheme.spacing4),
        Text(
          value,
          style: MerchantTheme.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: MerchantTheme.spacing8,
        vertical: MerchantTheme.spacing4,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withOpacity(0.1),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusSmall),
      ),
      child: Text(
        status.toUpperCase(),
        style: MerchantTheme.labelSmall.copyWith(
          color: _getStatusColor(status),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildPerformanceChip(String performance) {
    Color color;
    switch (performance) {
      case 'Top Performer':
        color = MerchantTheme.successGreen;
        break;
      case 'Average':
        color = MerchantTheme.warningOrange;
        break;
      case 'Below Average':
        color = MerchantTheme.errorRed;
        break;
      case 'New':
        color = MerchantTheme.secondaryBlue;
        break;
      default:
        color = MerchantTheme.neutral500;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: MerchantTheme.spacing8,
        vertical: MerchantTheme.spacing4,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(MerchantTheme.radiusSmall),
      ),
      child: Text(
        performance.toUpperCase(),
        style: MerchantTheme.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(MerchantTheme.spacing64),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.people_outlined, size: 80, color: MerchantTheme.neutral400),
          const SizedBox(height: MerchantTheme.spacing24),
          Text(
            'No sales representatives found',
            style: MerchantTheme.headline3.copyWith(color: MerchantTheme.neutral600),
          ),
          const SizedBox(height: MerchantTheme.spacing12),
          Text(
            'Try adjusting your search or filters',
            style: MerchantTheme.bodyLarge.copyWith(color: MerchantTheme.neutral500),
          ),
          const SizedBox(height: MerchantTheme.spacing32),
          ElevatedButton.icon(
            onPressed: _showAddSalesRepDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Sales Rep'),
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Color _getStatusColor(String status) {
    switch (status) {
      case 'Active':
        return MerchantTheme.successGreen;
      case 'Inactive':
        return MerchantTheme.neutral500;
      case 'On Leave':
        return MerchantTheme.warningOrange;
      case 'Terminated':
        return MerchantTheme.errorRed;
      default:
        return MerchantTheme.neutral500;
    }
  }

  // Action Methods
  void _showAddSalesRepDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add Sales Representative - Coming Soon!'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _viewSalesRepDetails(Map<String, dynamic> rep) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for ${rep['name']}'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _editSalesRep(Map<String, dynamic> rep) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Editing ${rep['name']}'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _viewPerformanceReport(Map<String, dynamic> rep) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing performance report for ${rep['name']}'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _manageCommission(Map<String, dynamic> rep) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Managing commission for ${rep['name']}'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }
}
