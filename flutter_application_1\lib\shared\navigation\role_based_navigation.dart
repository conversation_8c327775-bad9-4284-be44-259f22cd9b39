import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/enums/user_role.dart';
import '../middleware/auth_middleware.dart';
import '../../providers/auth_provider.dart';
import '../../providers/demo_auth_provider.dart';

/// Role-based navigation helper
class RoleBasedNavigation {
  /// Navigate to appropriate dashboard based on user role
  static void navigateToDashboard(BuildContext context, WidgetRef ref) {
    final user = AuthMiddleware.getCurrentUser(ref);
    if (user == null) {
      context.go('/login');
      return;
    }

    switch (user.role) {
      case UserRole.customer:
        context.go('/customer');
        break;
      case UserRole.salesRep:
        context.go('/sales-rep');
        break;
      case UserRole.merchant:
        context.go('/merchant');
        break;
      case UserRole.admin:
        context.go('/admin');
        break;
      case UserRole.boss:
        context.go('/boss');
        break;
    }
  }

  /// Get navigation items based on user role
  static List<NavigationItem> getNavigationItems(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return [
          const NavigationItem(
            icon: Icons.home,
            label: 'Home',
            route: '/customer',
          ),
          const NavigationItem(
            icon: Icons.qr_code_scanner,
            label: 'Scanner',
            route: '/customer/scanner',
          ),
          const NavigationItem(
            icon: Icons.shopping_cart,
            label: 'Cart',
            route: '/customer/cart',
          ),
          const NavigationItem(
            icon: Icons.history,
            label: 'History',
            route: '/customer/history',
          ),
          const NavigationItem(
            icon: Icons.person,
            label: 'Profile',
            route: '/customer/profile',
          ),
        ];

      case UserRole.salesRep:
        return [
          const NavigationItem(
            icon: Icons.dashboard,
            label: 'Dashboard',
            route: '/sales-rep',
          ),
          const NavigationItem(
            icon: Icons.qr_code_scanner,
            label: 'QR Verification',
            route: '/sales-rep',
          ),
          const NavigationItem(
            icon: Icons.receipt,
            label: 'Receipts',
            route: '/sales-rep',
          ),
          const NavigationItem(
            icon: Icons.analytics,
            label: 'Reports',
            route: '/sales-rep',
          ),
          const NavigationItem(
            icon: Icons.people,
            label: 'Customers',
            route: '/sales-rep',
          ),
        ];

      case UserRole.merchant:
        return [
          const NavigationItem(
            icon: Icons.dashboard,
            label: 'Dashboard',
            route: '/merchant',
          ),
          const NavigationItem(
            icon: Icons.inventory,
            label: 'Inventory',
            route: '/merchant/inventory',
          ),
          const NavigationItem(
            icon: Icons.analytics,
            label: 'Analytics',
            route: '/merchant',
          ),
          const NavigationItem(
            icon: Icons.settings,
            label: 'Settings',
            route: '/merchant',
          ),
        ];

      case UserRole.admin:
        return [
          const NavigationItem(
            icon: Icons.dashboard,
            label: 'Dashboard',
            route: '/admin',
          ),
          const NavigationItem(
            icon: Icons.store,
            label: 'Merchants',
            route: '/admin',
          ),
          const NavigationItem(
            icon: Icons.analytics,
            label: 'Analytics',
            route: '/admin',
          ),
          const NavigationItem(
            icon: Icons.monitor,
            label: 'Live Monitor',
            route: '/admin',
          ),
          const NavigationItem(
            icon: Icons.people,
            label: 'Users',
            route: '/admin',
          ),
          const NavigationItem(
            icon: Icons.settings,
            label: 'Settings',
            route: '/admin',
          ),
        ];

      case UserRole.boss:
        return [
          const NavigationItem(
            icon: Icons.dashboard,
            label: 'Dashboard',
            route: '/boss',
          ),
          const NavigationItem(
            icon: Icons.analytics,
            label: 'Multi-Mall Analytics',
            route: '/boss',
          ),
          const NavigationItem(
            icon: Icons.admin_panel_settings,
            label: 'Admin Management',
            route: '/boss',
          ),
          const NavigationItem(
            icon: Icons.account_balance,
            label: 'Financial Reports',
            route: '/boss',
          ),
          const NavigationItem(
            icon: Icons.history,
            label: 'Audit Trails',
            route: '/boss',
          ),
          const NavigationItem(
            icon: Icons.settings,
            label: 'Settings',
            route: '/boss',
          ),
        ];
    }
  }

  /// Check if user can access a specific route
  static bool canAccessRoute(WidgetRef ref, String route) {
    final user = AuthMiddleware.getCurrentUser(ref);
    if (user == null) return false;

    // Define route access rules
    final routeAccess = {
      '/customer': [UserRole.customer],
      '/sales-rep': [UserRole.salesRep],
      '/merchant': [UserRole.merchant],
      '/admin': [UserRole.admin],
      '/boss': [UserRole.boss],
    };

    // Check if route starts with any protected path
    for (final entry in routeAccess.entries) {
      if (route.startsWith(entry.key)) {
        return entry.value.contains(user.role);
      }
    }

    // Public routes
    return true;
  }

  /// Get breadcrumb navigation for current route
  static List<BreadcrumbItem> getBreadcrumbs(
      String currentRoute, UserRole role) {
    final breadcrumbs = <BreadcrumbItem>[];

    // Add home breadcrumb
    breadcrumbs.add(BreadcrumbItem(
      label: _getRoleDisplayName(role),
      route: AuthMiddleware.getDashboardRoute(role),
    ));

    // Add specific breadcrumbs based on route
    if (currentRoute.contains('/inventory')) {
      breadcrumbs.add(const BreadcrumbItem(
        label: 'Inventory',
        route: '/merchant/inventory',
      ));
    } else if (currentRoute.contains('/scanner')) {
      breadcrumbs.add(const BreadcrumbItem(
        label: 'Scanner',
        route: '/customer/scanner',
      ));
    } else if (currentRoute.contains('/cart')) {
      breadcrumbs.add(const BreadcrumbItem(
        label: 'Cart',
        route: '/customer/cart',
      ));
    }

    return breadcrumbs;
  }

  /// Get role display name
  static String _getRoleDisplayName(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.salesRep:
        return 'Sales Rep';
      case UserRole.merchant:
        return 'Merchant';
      case UserRole.admin:
        return 'Admin';
      case UserRole.boss:
        return 'Executive';
    }
  }

  /// Navigate with role validation
  static void navigateWithValidation(
    BuildContext context,
    WidgetRef ref,
    String route,
  ) {
    if (canAccessRoute(ref, route)) {
      context.go(route);
    } else {
      // Show unauthorized message or redirect to appropriate dashboard
      navigateToDashboard(context, ref);
    }
  }
}

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;
  final String route;
  final bool isActive;

  const NavigationItem({
    required this.icon,
    required this.label,
    required this.route,
    this.isActive = false,
  });

  NavigationItem copyWith({
    IconData? icon,
    String? label,
    String? route,
    bool? isActive,
  }) {
    return NavigationItem(
      icon: icon ?? this.icon,
      label: label ?? this.label,
      route: route ?? this.route,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// Breadcrumb item model
class BreadcrumbItem {
  final String label;
  final String route;

  const BreadcrumbItem({
    required this.label,
    required this.route,
  });
}

/// Provider for role-based navigation
final roleBasedNavigationProvider = Provider<RoleBasedNavigation>((ref) {
  return RoleBasedNavigation();
});

/// Provider for current user navigation items
final currentUserNavigationItemsProvider =
    Provider<List<NavigationItem>>((ref) {
  try {
    final authState = ref.read(authControllerProvider);
    final demoUser = ref.read(demoUserProvider);

    final user = authState.when(
          data: (user) => user,
          loading: () => null,
          error: (_, __) => null,
        ) ??
        demoUser;

    if (user == null) return [];

    return RoleBasedNavigation.getNavigationItems(user.role);
  } catch (e) {
    return [];
  }
});
