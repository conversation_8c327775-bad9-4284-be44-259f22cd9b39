import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../providers/product_provider.dart';
import '../../../providers/cart_provider.dart';
import '../../../providers/auth_provider.dart';
import '../../../shared/themes/customer_theme.dart';
import '../../../shared/widgets/loading_button.dart';
import '../widgets/product_card.dart';
import '../widgets/premium_ui_components.dart';

class ScannerScreen extends ConsumerStatefulWidget {
  const ScannerScreen({super.key});

  @override
  ConsumerState<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends ConsumerState<ScannerScreen>
    with TickerProviderStateMixin {
  MobileScannerController? _scannerController;
  final _manualBarcodeController = TextEditingController();
  bool _isLoading = false;
  bool _isCameraMode = true;
  bool _isFlashOn = false;
  String? _lastScannedCode;
  DateTime? _lastScanTime;

  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeScanner();
    _initializeAnimations();
  }

  void _initializeScanner() {
    _scannerController = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      facing: CameraFacing.back,
      torchEnabled: false,
    );
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);

    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _scannerController?.dispose();
    _manualBarcodeController.dispose();
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onBarcodeDetected(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    final barcode = barcodes.first;
    final code = barcode.rawValue;

    if (code == null || code.isEmpty) return;

    // Prevent duplicate scans within 2 seconds
    final now = DateTime.now();
    if (_lastScannedCode == code &&
        _lastScanTime != null &&
        now.difference(_lastScanTime!).inSeconds < 2) {
      return;
    }

    _lastScannedCode = code;
    _lastScanTime = now;

    // Provide haptic feedback
    HapticFeedback.lightImpact();

    _searchProduct(code);
  }

  Future<void> _searchProduct(String barcode) async {
    if (barcode.trim().isEmpty) return;

    setState(() => _isLoading = true);

    try {
      // Get current mall ID
      final user = ref.read(currentUserProvider).value;
      if (user?.mallId == null) {
        _showErrorDialog(
            'Mall not detected. Please ensure you are in a registered mall.');
        return;
      }

      // Look up product by barcode
      final productController = ref.read(productControllerProvider.notifier);
      final product = await productController.getProductByBarcode(
          barcode.trim(), user!.mallId!);

      if (product != null) {
        _showProductDialog(product);
        _manualBarcodeController.clear(); // Clear for next scan
      } else {
        _showErrorDialog(
            'Product not found. This item may not be available in this mall.');
      }
    } catch (e) {
      _showErrorDialog('Error searching product: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
    _scannerController?.toggleTorch();
  }

  void _switchCamera() {
    _scannerController?.switchCamera();
  }

  void _toggleScanMode() {
    setState(() {
      _isCameraMode = !_isCameraMode;
    });
  }

  void _showProductDialog(product) {
    showDialog(
      context: context,
      builder: (context) => ProductDialog(product: product),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Result'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          _isCameraMode ? Colors.black : CustomerTheme.backgroundLight,
      body: Stack(
        children: [
          // Camera Scanner View
          if (_isCameraMode && _scannerController != null)
            FadeTransition(
              opacity: _fadeAnimation,
              child: MobileScanner(
                controller: _scannerController!,
                onDetect: _onBarcodeDetected,
              ),
            ),

          // Manual Input Mode
          if (!_isCameraMode)
            Container(
              color: CustomerTheme.backgroundLight,
              child: _buildManualInputMode(),
            ),

          // Top Controls
          Positioned(
            top: MediaQuery.of(context).padding.top + CustomerTheme.spacing16,
            left: CustomerTheme.spacing16,
            right: CustomerTheme.spacing16,
            child: _buildTopControls(),
          ),

          // Scanner Overlay (only in camera mode)
          if (_isCameraMode)
            Positioned.fill(
              child: _buildScannerOverlay(),
            ),

          // Bottom Controls
          Positioned(
            bottom:
                MediaQuery.of(context).padding.bottom + CustomerTheme.spacing16,
            left: CustomerTheme.spacing16,
            right: CustomerTheme.spacing16,
            child: _buildBottomControls(),
          ),

          // Professional Loading Overlay
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const PremiumLoadingIndicator(
                      size: 56,
                      color: Colors.white,
                      strokeWidth: 4,
                    ),
                    const SizedBox(height: CustomerTheme.spacing24),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: CustomerTheme.spacing24,
                        vertical: CustomerTheme.spacing12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius:
                            BorderRadius.circular(CustomerTheme.largeRadius),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Searching product...',
                        style: CustomerTheme.bodyLarge.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Back button
        Container(
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(25),
          ),
          child: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
        ),

        // Title
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            _isCameraMode ? 'Scan Barcode' : 'Enter Barcode',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // Flash toggle (only in camera mode)
        if (_isCameraMode)
          Container(
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(25),
            ),
            child: IconButton(
              onPressed: _toggleFlash,
              icon: Icon(
                _isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: _isFlashOn ? Colors.yellow : Colors.white,
              ),
            ),
          )
        else
          const SizedBox(width: 48),
      ],
    );
  }

  Widget _buildScannerOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
      ),
      child: Stack(
        children: [
          // Scanning area cutout
          Center(
            child: Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // Corner indicators
                  ...List.generate(4, (index) {
                    return Positioned(
                      top: index < 2 ? 0 : null,
                      bottom: index >= 2 ? 0 : null,
                      left: index % 2 == 0 ? 0 : null,
                      right: index % 2 == 1 ? 0 : null,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border(
                            top: index < 2
                                ? const BorderSide(
                                    color: Colors.green, width: 4)
                                : BorderSide.none,
                            bottom: index >= 2
                                ? const BorderSide(
                                    color: Colors.green, width: 4)
                                : BorderSide.none,
                            left: index % 2 == 0
                                ? const BorderSide(
                                    color: Colors.green, width: 4)
                                : BorderSide.none,
                            right: index % 2 == 1
                                ? const BorderSide(
                                    color: Colors.green, width: 4)
                                : BorderSide.none,
                          ),
                        ),
                      ),
                    );
                  }),

                  // Scanning line animation
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    child: Container(
                      height: 2,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.transparent,
                            Colors.green.withOpacity(0.8),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                    builder: (context, child) {
                      return Positioned(
                        top: 125 * _pulseAnimation.value,
                        left: 0,
                        right: 0,
                        child: child!,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // Instructions
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'Position barcode within the frame',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    'Supports UPC, EAN, Code 128 formats',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Mode toggle
        Container(
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(25),
          ),
          child: IconButton(
            onPressed: _toggleScanMode,
            icon: Icon(
              _isCameraMode ? Icons.keyboard : Icons.camera_alt,
              color: Colors.white,
            ),
            tooltip: _isCameraMode ? 'Manual Entry' : 'Camera Scan',
          ),
        ),

        // Cart button with item count
        Consumer(
          builder: (context, ref, child) {
            final itemCount = ref.watch(cartItemCountProvider);
            return Container(
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Stack(
                children: [
                  IconButton(
                    onPressed: () =>
                        Navigator.of(context).pushNamed('/customer/cart'),
                    icon: const Icon(Icons.shopping_cart, color: Colors.white),
                  ),
                  if (itemCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$itemCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),

        // Switch camera (only in camera mode)
        if (_isCameraMode)
          Container(
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(25),
            ),
            child: IconButton(
              onPressed: _switchCamera,
              icon: const Icon(Icons.flip_camera_ios, color: Colors.white),
              tooltip: 'Switch Camera',
            ),
          )
        else
          const SizedBox(width: 48),
      ],
    );
  }

  Widget _buildManualInputMode() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.teal[400]!, Colors.teal[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.teal.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Column(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 12),
                Text(
                  'Enter Product Barcode',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Type the barcode number manually',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Barcode input
          TextFormField(
            controller: _manualBarcodeController,
            decoration: InputDecoration(
              labelText: 'Product Barcode',
              hintText: 'Enter barcode number',
              prefixIcon: const Icon(Icons.qr_code),
              suffixIcon: _isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(12),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : IconButton(
                      onPressed: () =>
                          _searchProduct(_manualBarcodeController.text),
                      icon: const Icon(Icons.search),
                    ),
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.search,
            onFieldSubmitted: _searchProduct,
            enabled: !_isLoading,
          ),

          const SizedBox(height: 24),

          // Search button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed:
                  _isLoading || _manualBarcodeController.text.trim().isEmpty
                      ? null
                      : () => _searchProduct(_manualBarcodeController.text),
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.search),
              label: Text(_isLoading ? 'Searching...' : 'Search Product'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ProductDialog extends ConsumerStatefulWidget {
  final dynamic product;

  const ProductDialog({super.key, required this.product});

  @override
  ConsumerState<ProductDialog> createState() => _ProductDialogState();
}

class _ProductDialogState extends ConsumerState<ProductDialog> {
  int _quantity = 1;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Product info
            ProductCard(product: widget.product),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Quantity selector
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: _quantity > 1
                            ? () => setState(() => _quantity--)
                            : null,
                        icon: const Icon(Icons.remove),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '$_quantity',
                          style: const TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      IconButton(
                        onPressed: () => setState(() => _quantity++),
                        icon: const Icon(Icons.add),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Total price
                  Text(
                    'Total: ₦${(widget.product.pricePerUnit * _quantity).toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),

                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: LoadingButton(
                          onPressed: () {
                            ref.read(cartProvider.notifier).addItem(
                                  widget.product,
                                  quantity: _quantity,
                                );
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Added ${widget.product.name} to cart'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          },
                          child: const Text('Add to Cart'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
