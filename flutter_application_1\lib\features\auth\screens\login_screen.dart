import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../services/auth_service.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_button.dart';
import '../../../providers/demo_auth_provider.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Check if it's a demo user
      if (password == 'password' && email.contains('demo.com')) {
        // Use demo auth
        ref.read(demoUserProvider.notifier).loginDemoUser(email);

        if (mounted) {
          // Navigate based on email
          if (email == '<EMAIL>') {
            context.go('/customer');
          } else if (email == '<EMAIL>') {
            context.go('/merchant');
          } else if (email == '<EMAIL>') {
            context.go('/sales-rep');
          } else if (email == '<EMAIL>') {
            context.go('/admin');
          } else if (email == '<EMAIL>') {
            context.go('/boss');
          } else {
            _showErrorSnackBar('Invalid demo credentials');
          }
        }
      } else {
        // Use Firebase auth for real users
        final user = await AuthService.instance.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        if (user != null && mounted) {
          // Navigate based on user role
          switch (user.role.value) {
            case 'customer':
              context.go('/customer');
              break;
            case 'sales_rep':
              context.go('/sales-rep');
              break;
            case 'merchant':
              context.go('/merchant');
              break;
            case 'admin':
              context.go('/admin');
              break;
            case 'boss':
              context.go('/boss');
              break;
            default:
              _showErrorSnackBar('Invalid user role');
          }
        }
      }
    } catch (e) {
      _showErrorSnackBar(e.toString());
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Logo
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.shopping_cart,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Title
                    Text(
                      'Welcome Back',
                      style: Theme.of(context).textTheme.headlineMedium,
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    Text(
                      'Sign in to your account',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                          ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 24),

                    // Demo Credentials Card
                    Card(
                      color: Colors.blue[50],
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info,
                                    color: Colors.blue[700], size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Demo Login Credentials',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue[700],
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                                'Use these credentials to test different roles:',
                                style: TextStyle(fontSize: 12)),
                            const SizedBox(height: 4),
                            _buildDemoCredential(
                                'Customer', '<EMAIL>'),
                            _buildDemoCredential(
                                'Merchant', '<EMAIL>'),
                            _buildDemoCredential(
                                'Sales Rep', '<EMAIL>'),
                            _buildDemoCredential('Admin', '<EMAIL>'),
                            _buildDemoCredential('Boss', '<EMAIL>'),
                            const SizedBox(height: 4),
                            Text(
                              'Password for all: password',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Email Field
                    CustomTextField(
                      controller: _emailController,
                      label: 'Email',
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: Icons.email_outlined,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                            .hasMatch(value)) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Password Field
                    CustomTextField(
                      controller: _passwordController,
                      label: 'Password',
                      obscureText: _obscurePassword,
                      prefixIcon: Icons.lock_outlined,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() => _obscurePassword = !_obscurePassword);
                        },
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your password';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // Login Button
                    LoadingButton(
                      onPressed: _handleLogin,
                      isLoading: _isLoading,
                      child: const Text('Sign In'),
                    ),

                    const SizedBox(height: 16),

                    // Register Link
                    TextButton(
                      onPressed: () => context.go('/register'),
                      child: const Text('Don\'t have an account? Sign Up'),
                    ),

                    const SizedBox(height: 8),

                    // Forgot Password Link
                    TextButton(
                      onPressed: () {
                        // TODO: Implement forgot password
                        _showErrorSnackBar(
                            'Forgot password feature coming soon');
                      },
                      child: const Text('Forgot Password?'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDemoCredential(String role, String email) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        children: [
          Text(
            '$role: ',
            style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
          ),
          Expanded(
            child: Text(
              email,
              style: TextStyle(fontSize: 11, color: Colors.blue[600]),
            ),
          ),
        ],
      ),
    );
  }
}
