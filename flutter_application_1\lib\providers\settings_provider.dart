import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../services/firebase_service.dart';

// Settings model
class MerchantSettings {
  final String merchantId;
  final String businessName;
  final String businessDescription;
  final String businessAddress;
  final String businessPhone;
  final String businessEmail;
  final String? businessWebsite;
  final String? businessLogo;
  final String taxId;
  final String registrationNumber;
  final Map<String, dynamic> paymentSettings;
  final Map<String, dynamic> notificationSettings;
  final Map<String, dynamic> inventorySettings;
  final Map<String, dynamic> orderSettings;
  final Map<String, dynamic> reportSettings;
  final DateTime updatedAt;

  MerchantSettings({
    required this.merchantId,
    required this.businessName,
    required this.businessDescription,
    required this.businessAddress,
    required this.businessPhone,
    required this.businessEmail,
    this.businessWebsite,
    this.businessLogo,
    required this.taxId,
    required this.registrationNumber,
    required this.paymentSettings,
    required this.notificationSettings,
    required this.inventorySettings,
    required this.orderSettings,
    required this.reportSettings,
    required this.updatedAt,
  });

  factory MerchantSettings.defaultSettings(String merchantId) {
    return MerchantSettings(
      merchantId: merchantId,
      businessName: 'My Business',
      businessDescription: 'A great business serving customers',
      businessAddress: '',
      businessPhone: '',
      businessEmail: '',
      taxId: '',
      registrationNumber: '',
      paymentSettings: {
        'paystackEnabled': true,
        'paystackPublicKey': '',
        'paystackSecretKey': '',
        'acceptCash': true,
        'acceptTransfer': true,
        'acceptCard': true,
      },
      notificationSettings: {
        'emailNotifications': true,
        'smsNotifications': false,
        'pushNotifications': true,
        'lowStockAlerts': true,
        'newOrderAlerts': true,
        'paymentAlerts': true,
        'dailyReports': false,
        'weeklyReports': true,
        'monthlyReports': true,
      },
      inventorySettings: {
        'autoReorderEnabled': false,
        'autoReorderThreshold': 5,
        'defaultLowStockThreshold': 10,
        'trackExpiryDates': false,
        'enableBarcodeScanning': true,
        'requireApprovalForStockChanges': false,
      },
      orderSettings: {
        'autoConfirmOrders': false,
        'requireSignatureOnDelivery': false,
        'allowPartialDeliveries': true,
        'defaultShippingFee': 0.0,
        'freeShippingThreshold': 50000.0,
        'orderExpiryHours': 24,
      },
      reportSettings: {
        'defaultReportPeriod': 'monthly',
        'includeCharts': true,
        'includeComparisons': true,
        'autoExportReports': false,
        'reportFormat': 'pdf',
        'emailReports': false,
      },
      updatedAt: DateTime.now(),
    );
  }

  factory MerchantSettings.fromJson(Map<String, dynamic> json) {
    return MerchantSettings(
      merchantId: json['merchantId'] ?? '',
      businessName: json['businessName'] ?? 'My Business',
      businessDescription: json['businessDescription'] ?? '',
      businessAddress: json['businessAddress'] ?? '',
      businessPhone: json['businessPhone'] ?? '',
      businessEmail: json['businessEmail'] ?? '',
      businessWebsite: json['businessWebsite'],
      businessLogo: json['businessLogo'],
      taxId: json['taxId'] ?? '',
      registrationNumber: json['registrationNumber'] ?? '',
      paymentSettings: Map<String, dynamic>.from(json['paymentSettings'] ?? {}),
      notificationSettings:
          Map<String, dynamic>.from(json['notificationSettings'] ?? {}),
      inventorySettings:
          Map<String, dynamic>.from(json['inventorySettings'] ?? {}),
      orderSettings: Map<String, dynamic>.from(json['orderSettings'] ?? {}),
      reportSettings: Map<String, dynamic>.from(json['reportSettings'] ?? {}),
      updatedAt:
          DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'merchantId': merchantId,
      'businessName': businessName,
      'businessDescription': businessDescription,
      'businessAddress': businessAddress,
      'businessPhone': businessPhone,
      'businessEmail': businessEmail,
      'businessWebsite': businessWebsite,
      'businessLogo': businessLogo,
      'taxId': taxId,
      'registrationNumber': registrationNumber,
      'paymentSettings': paymentSettings,
      'notificationSettings': notificationSettings,
      'inventorySettings': inventorySettings,
      'orderSettings': orderSettings,
      'reportSettings': reportSettings,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  MerchantSettings copyWith({
    String? businessName,
    String? businessDescription,
    String? businessAddress,
    String? businessPhone,
    String? businessEmail,
    String? businessWebsite,
    String? businessLogo,
    String? taxId,
    String? registrationNumber,
    Map<String, dynamic>? paymentSettings,
    Map<String, dynamic>? notificationSettings,
    Map<String, dynamic>? inventorySettings,
    Map<String, dynamic>? orderSettings,
    Map<String, dynamic>? reportSettings,
    DateTime? updatedAt,
  }) {
    return MerchantSettings(
      merchantId: merchantId,
      businessName: businessName ?? this.businessName,
      businessDescription: businessDescription ?? this.businessDescription,
      businessAddress: businessAddress ?? this.businessAddress,
      businessPhone: businessPhone ?? this.businessPhone,
      businessEmail: businessEmail ?? this.businessEmail,
      businessWebsite: businessWebsite ?? this.businessWebsite,
      businessLogo: businessLogo ?? this.businessLogo,
      taxId: taxId ?? this.taxId,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      paymentSettings: paymentSettings ?? this.paymentSettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      inventorySettings: inventorySettings ?? this.inventorySettings,
      orderSettings: orderSettings ?? this.orderSettings,
      reportSettings: reportSettings ?? this.reportSettings,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Settings repository
class SettingsRepository {
  final FirebaseService _firebase = FirebaseService.instance;

  Future<MerchantSettings> getSettings(String merchantId) async {
    try {
      final doc = await _firebase.firestore
          .collection('merchant_settings')
          .doc(merchantId)
          .get();

      if (doc.exists) {
        return MerchantSettings.fromJson(doc.data()!);
      } else {
        // Return default settings if none exist
        return MerchantSettings.defaultSettings(merchantId);
      }
    } catch (e) {
      return MerchantSettings.defaultSettings(merchantId);
    }
  }

  Future<void> updateSettings(MerchantSettings settings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(settings.merchantId)
        .set(settings.toJson());
  }

  Future<void> updateBusinessInfo({
    required String merchantId,
    required String businessName,
    required String businessDescription,
    required String businessAddress,
    required String businessPhone,
    required String businessEmail,
    String? businessWebsite,
    String? businessLogo,
    required String taxId,
    required String registrationNumber,
  }) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'businessName': businessName,
      'businessDescription': businessDescription,
      'businessAddress': businessAddress,
      'businessPhone': businessPhone,
      'businessEmail': businessEmail,
      'businessWebsite': businessWebsite,
      'businessLogo': businessLogo,
      'taxId': taxId,
      'registrationNumber': registrationNumber,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> updatePaymentSettings(
      String merchantId, Map<String, dynamic> paymentSettings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'paymentSettings': paymentSettings,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> updateNotificationSettings(
      String merchantId, Map<String, dynamic> notificationSettings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'notificationSettings': notificationSettings,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> updateInventorySettings(
      String merchantId, Map<String, dynamic> inventorySettings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'inventorySettings': inventorySettings,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> updateOrderSettings(
      String merchantId, Map<String, dynamic> orderSettings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'orderSettings': orderSettings,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  Future<void> updateReportSettings(
      String merchantId, Map<String, dynamic> reportSettings) async {
    await _firebase.firestore
        .collection('merchant_settings')
        .doc(merchantId)
        .update({
      'reportSettings': reportSettings,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }
}

// Demo Settings Repository for testing
class DemoSettingsRepository {
  static MerchantSettings? _settings;

  Future<MerchantSettings> getSettings(String merchantId) async {
    await Future.delayed(const Duration(milliseconds: 500));

    if (_settings == null) {
      _settings = MerchantSettings.defaultSettings(merchantId);

      // Load from SharedPreferences if available
      try {
        final prefs = await SharedPreferences.getInstance();
        final settingsJson = prefs.getString('merchant_settings_$merchantId');
        if (settingsJson != null) {
          _settings = MerchantSettings.fromJson(json.decode(settingsJson));
        }
      } catch (e) {
        // Ignore errors and use default settings
      }
    }

    return _settings!;
  }

  Future<void> updateSettings(MerchantSettings settings) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _settings = settings.copyWith(updatedAt: DateTime.now());

    // Save to SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('merchant_settings_${settings.merchantId}',
          json.encode(_settings!.toJson()));
    } catch (e) {
      // Ignore errors
    }
  }

  Future<void> updateBusinessInfo({
    required String merchantId,
    required String businessName,
    required String businessDescription,
    required String businessAddress,
    required String businessPhone,
    required String businessEmail,
    String? businessWebsite,
    String? businessLogo,
    required String taxId,
    required String registrationNumber,
  }) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        businessName: businessName,
        businessDescription: businessDescription,
        businessAddress: businessAddress,
        businessPhone: businessPhone,
        businessEmail: businessEmail,
        businessWebsite: businessWebsite,
        businessLogo: businessLogo,
        taxId: taxId,
        registrationNumber: registrationNumber,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }

  Future<void> updatePaymentSettings(
      String merchantId, Map<String, dynamic> paymentSettings) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        paymentSettings: paymentSettings,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }

  Future<void> updateNotificationSettings(
      String merchantId, Map<String, dynamic> notificationSettings) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        notificationSettings: notificationSettings,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }

  Future<void> updateInventorySettings(
      String merchantId, Map<String, dynamic> inventorySettings) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        inventorySettings: inventorySettings,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }

  Future<void> updateOrderSettings(
      String merchantId, Map<String, dynamic> orderSettings) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        orderSettings: orderSettings,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }

  Future<void> updateReportSettings(
      String merchantId, Map<String, dynamic> reportSettings) async {
    if (_settings != null) {
      _settings = _settings!.copyWith(
        reportSettings: reportSettings,
        updatedAt: DateTime.now(),
      );
      await updateSettings(_settings!);
    }
  }
}

// Providers
final settingsRepositoryProvider =
    Provider<SettingsRepository>((ref) => SettingsRepository());
final demoSettingsRepositoryProvider =
    Provider<DemoSettingsRepository>((ref) => DemoSettingsRepository());

// Current user's settings provider (simplified for demo)
final currentUserSettingsProvider =
    FutureProvider<MerchantSettings>((ref) async {
  final repository = ref.watch(demoSettingsRepositoryProvider);
  // For demo, always return settings for demo merchant
  return await repository.getSettings('demo-merchant');
});

// Settings management notifier
class SettingsNotifier extends StateNotifier<AsyncValue<MerchantSettings?>> {
  SettingsNotifier(this._repository) : super(const AsyncValue.data(null));

  final DemoSettingsRepository _repository;

  Future<void> updateBusinessInfo({
    required String merchantId,
    required String businessName,
    required String businessDescription,
    required String businessAddress,
    required String businessPhone,
    required String businessEmail,
    String? businessWebsite,
    String? businessLogo,
    required String taxId,
    required String registrationNumber,
  }) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateBusinessInfo(
        merchantId: merchantId,
        businessName: businessName,
        businessDescription: businessDescription,
        businessAddress: businessAddress,
        businessPhone: businessPhone,
        businessEmail: businessEmail,
        businessWebsite: businessWebsite,
        businessLogo: businessLogo,
        taxId: taxId,
        registrationNumber: registrationNumber,
      );
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updatePaymentSettings(
      String merchantId, Map<String, dynamic> paymentSettings) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updatePaymentSettings(merchantId, paymentSettings);
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateNotificationSettings(
      String merchantId, Map<String, dynamic> notificationSettings) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateNotificationSettings(
          merchantId, notificationSettings);
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateInventorySettings(
      String merchantId, Map<String, dynamic> inventorySettings) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateInventorySettings(merchantId, inventorySettings);
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateOrderSettings(
      String merchantId, Map<String, dynamic> orderSettings) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateOrderSettings(merchantId, orderSettings);
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateReportSettings(
      String merchantId, Map<String, dynamic> reportSettings) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateReportSettings(merchantId, reportSettings);
      final updatedSettings = await _repository.getSettings(merchantId);
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

final settingsNotifierProvider =
    StateNotifierProvider<SettingsNotifier, AsyncValue<MerchantSettings?>>(
        (ref) {
  final repository = ref.watch(demoSettingsRepositoryProvider);
  return SettingsNotifier(repository);
});
