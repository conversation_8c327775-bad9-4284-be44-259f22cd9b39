import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ProfessionalDataTable<T> extends StatefulWidget {
  final List<DataTableColumn<T>> columns;
  final List<T> data;
  final Function(T)? onRowTap;
  final Function(List<T>)? onSelectionChanged;
  final bool allowSelection;
  final bool allowSorting;
  final bool allowFiltering;
  final bool showCheckboxColumn;
  final double rowHeight;
  final bool isLoading;
  final Widget? emptyState;
  final String? searchQuery;
  final Function(String, bool)? onSort;
  final String? currentSortColumn;
  final bool isAscending;

  const ProfessionalDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.onRowTap,
    this.onSelectionChanged,
    this.allowSelection = true,
    this.allowSorting = true,
    this.allowFiltering = true,
    this.showCheckboxColumn = true,
    this.rowHeight = 56.0,
    this.isLoading = false,
    this.emptyState,
    this.searchQuery,
    this.onSort,
    this.currentSortColumn,
    this.isAscending = true,
  });

  @override
  State<ProfessionalDataTable<T>> createState() => _ProfessionalDataTableState<T>();
}

class _ProfessionalDataTableState<T> extends State<ProfessionalDataTable<T>> {
  final Set<T> _selectedItems = {};
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onSelectAll(bool? selected) {
    setState(() {
      if (selected == true) {
        _selectedItems.addAll(widget.data);
      } else {
        _selectedItems.clear();
      }
    });
    widget.onSelectionChanged?.call(_selectedItems.toList());
  }

  void _onSelectItem(T item, bool? selected) {
    setState(() {
      if (selected == true) {
        _selectedItems.add(item);
      } else {
        _selectedItems.remove(item);
      }
    });
    widget.onSelectionChanged?.call(_selectedItems.toList());
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    if (widget.data.isEmpty) {
      return widget.emptyState ?? _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Data rows
          Expanded(
            child: Scrollbar(
              controller: _scrollController,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: _buildDataRows(),
              ),
            ),
          ),
          
          // Footer with selection info
          if (_selectedItems.isNotEmpty) _buildSelectionFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          // Select all checkbox
          if (widget.showCheckboxColumn && widget.allowSelection)
            SizedBox(
              width: 60,
              child: Checkbox(
                value: _selectedItems.length == widget.data.length && widget.data.isNotEmpty
                    ? true
                    : _selectedItems.isEmpty
                        ? false
                        : null,
                onChanged: _onSelectAll,
                tristate: true,
              ),
            ),
          
          // Column headers
          ...widget.columns.map((column) {
            return Expanded(
              flex: column.flex,
              child: _buildColumnHeader(column),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(DataTableColumn<T> column) {
    final isCurrentSort = widget.currentSortColumn == column.key;
    
    return InkWell(
      onTap: widget.allowSorting && column.sortable
          ? () => widget.onSort?.call(column.key, !widget.isAscending)
          : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Text(
                column.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isCurrentSort 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: column.alignment == Alignment.centerRight
                    ? TextAlign.right
                    : TextAlign.left,
              ),
            ),
            if (widget.allowSorting && column.sortable) ...[
              const SizedBox(width: 4),
              AnimatedRotation(
                turns: isCurrentSort && !widget.isAscending ? 0.5 : 0,
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  Icons.arrow_upward,
                  size: 16,
                  color: isCurrentSort
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDataRows() {
    return Column(
      children: widget.data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isSelected = _selectedItems.contains(item);
        final isEven = index % 2 == 0;

        return _buildDataRow(item, isSelected, isEven, index);
      }).toList(),
    );
  }

  Widget _buildDataRow(T item, bool isSelected, bool isEven, int index) {
    return InkWell(
      onTap: () => widget.onRowTap?.call(item),
      child: Container(
        height: widget.rowHeight,
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : isEven
                  ? Theme.of(context).colorScheme.surface
                  : Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.1),
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            // Selection checkbox
            if (widget.showCheckboxColumn && widget.allowSelection)
              SizedBox(
                width: 60,
                child: Checkbox(
                  value: isSelected,
                  onChanged: (selected) => _onSelectItem(item, selected),
                ),
              ),
            
            // Data cells
            ...widget.columns.map((column) {
              return Expanded(
                flex: column.flex,
                child: _buildDataCell(column, item),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDataCell(DataTableColumn<T> column, T item) {
    final cellData = column.valueBuilder(item);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Align(
        alignment: column.alignment,
        child: column.cellBuilder?.call(item, cellData) ?? 
               _buildDefaultCell(cellData, column.alignment),
      ),
    );
  }

  Widget _buildDefaultCell(dynamic data, Alignment alignment) {
    if (data is Widget) return data;
    
    return Text(
      data?.toString() ?? '',
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        fontFamily: data is num ? 'monospace' : null,
      ),
      textAlign: alignment == Alignment.centerRight
          ? TextAlign.right
          : TextAlign.left,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSelectionFooter() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            '${_selectedItems.length} item${_selectedItems.length == 1 ? '' : 's'} selected',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() => _selectedItems.clear());
              widget.onSelectionChanged?.call([]);
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DataTableColumn<T> {
  final String key;
  final String title;
  final int flex;
  final bool sortable;
  final Alignment alignment;
  final dynamic Function(T) valueBuilder;
  final Widget Function(T item, dynamic value)? cellBuilder;

  const DataTableColumn({
    required this.key,
    required this.title,
    this.flex = 1,
    this.sortable = true,
    this.alignment = Alignment.centerLeft,
    required this.valueBuilder,
    this.cellBuilder,
  });
}
