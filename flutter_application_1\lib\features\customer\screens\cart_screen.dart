import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../providers/cart_provider.dart';
import '../../../shared/themes/customer_theme.dart';
import '../../../shared/widgets/loading_button.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/modern_ui_components.dart';

class CartScreen extends ConsumerWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cart = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: CustomerTheme.backgroundLight,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: CustomerTheme.surfaceWhite,
        foregroundColor: CustomerTheme.textPrimary,
        title: Text(
          'Shopping Cart',
          style: CustomerTheme.headingMedium.copyWith(
            color: CustomerTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (cart.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(right: CustomerTheme.spacing8),
              child: TextButton.icon(
                onPressed: () => _showClearCartDialog(context, ref),
                icon: const Icon(
                  Icons.delete_outline,
                  size: 18,
                  color: CustomerTheme.errorRed,
                ),
                label: Text(
                  'Clear',
                  style: CustomerTheme.labelMedium.copyWith(
                    color: CustomerTheme.errorRed,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: CustomerTheme.spacing12,
                    vertical: CustomerTheme.spacing8,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: cart.isEmpty
          ? _buildEmptyCart(context)
          : Column(
              children: [
                // Cart header with item count
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(CustomerTheme.spacing16),
                  decoration: BoxDecoration(
                    color: CustomerTheme.surfaceWhite,
                    border: Border(
                      bottom: BorderSide(
                        color: CustomerTheme.borderLight.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(CustomerTheme.spacing8),
                        decoration: BoxDecoration(
                          color: CustomerTheme.primaryTeal.withOpacity(0.1),
                          borderRadius:
                              BorderRadius.circular(CustomerTheme.spacing8),
                        ),
                        child: const Icon(
                          Icons.shopping_cart,
                          color: CustomerTheme.primaryTeal,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: CustomerTheme.spacing12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${cart.itemCount} ${cart.itemCount == 1 ? 'item' : 'items'} in cart',
                              style: CustomerTheme.bodyLarge.copyWith(
                                color: CustomerTheme.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'Subtotal: ₦${cart.subtotal.toStringAsFixed(2)}',
                              style: CustomerTheme.bodyMedium.copyWith(
                                color: CustomerTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Cart items
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(CustomerTheme.spacing16),
                    physics: const BouncingScrollPhysics(),
                    itemCount: cart.items.length,
                    itemBuilder: (context, index) {
                      final item = cart.items[index];
                      return Padding(
                        padding:
                            const EdgeInsets.only(bottom: CustomerTheme.spacing12),
                        child: CartItemCard(
                          item: item,
                          onQuantityChanged: (quantity) {
                            ref.read(cartProvider.notifier).updateItemQuantity(
                                  item.productId,
                                  quantity,
                                );
                          },
                          onRemove: () {
                            ref
                                .read(cartProvider.notifier)
                                .removeItem(item.productId);
                          },
                        ),
                      );
                    },
                  ),
                ),

                // Cart summary
                _buildCartSummary(context, ref, cart),
              ],
            ),
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            CustomerTheme.backgroundLight,
            CustomerTheme.primaryTeal.withOpacity(0.05),
          ],
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(CustomerTheme.spacingXL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated cart icon
              Container(
                padding: const EdgeInsets.all(CustomerTheme.spacingXL),
                decoration: BoxDecoration(
                  gradient: CustomerTheme.primaryGradient,
                  shape: BoxShape.circle,
                  boxShadow: CustomerTheme.elevatedShadow,
                ),
                child: const Icon(
                  Icons.shopping_cart_outlined,
                  size: 64,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: CustomerTheme.spacingXL),

              Text(
                'Your cart is empty',
                style: CustomerTheme.headingMedium.copyWith(
                  color: CustomerTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: CustomerTheme.spacingM),

              Text(
                'Start scanning products to add them to your cart and enjoy seamless shopping',
                style: CustomerTheme.bodyLarge.copyWith(
                  color: CustomerTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: CustomerTheme.spacingXXL),

              ModernButton(
                text: 'Start Scanning',
                icon: Icons.qr_code_scanner,
                gradient: CustomerTheme.primaryGradient,
                onPressed: () =>
                    Navigator.of(context).pop(), // Go back to scanner
                width: 200,
                height: 56,
              ),

              const SizedBox(height: CustomerTheme.spacingM),

              TextButton(
                onPressed: () {
                  // Navigate to browse products or categories
                },
                child: Text(
                  'Browse Products',
                  style: CustomerTheme.labelLarge.copyWith(
                    color: CustomerTheme.primaryTeal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCartSummary(
      BuildContext context, WidgetRef ref, CartState cart) {
    return Container(
      decoration: BoxDecoration(
        color: CustomerTheme.surfaceWhite,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(CustomerTheme.spacing24),
          topRight: Radius.circular(CustomerTheme.spacing24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 24,
            offset: const Offset(0, -8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, -2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(CustomerTheme.spacing20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: CustomerTheme.borderLight,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: CustomerTheme.spacing20),

              // Summary card
              Container(
                padding: const EdgeInsets.all(CustomerTheme.spacing16),
                decoration: CustomerTheme.getCardDecoration(
                  color: CustomerTheme.primaryTeal.withOpacity(0.05),
                ),
                child: Column(
                  children: [
                    // Summary rows
                    _buildSummaryRow(
                      context,
                      'Subtotal',
                      '₦${cart.subtotal.toStringAsFixed(2)}',
                    ),
                    const SizedBox(height: CustomerTheme.spacing8),
                    _buildSummaryRow(
                      context,
                      'Tax (7.5%)',
                      '₦${cart.tax.toStringAsFixed(2)}',
                    ),

                    const Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: CustomerTheme.spacing12),
                      child: Divider(
                        color: CustomerTheme.borderLight,
                        thickness: 1,
                      ),
                    ),

                    _buildSummaryRow(
                      context,
                      'Total',
                      '₦${cart.total.toStringAsFixed(2)}',
                      isTotal: true,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: CustomerTheme.spacing20),

              // Checkout button
              ModernButton(
                text:
                    'Proceed to Checkout (${cart.itemCount} ${cart.itemCount == 1 ? 'item' : 'items'})',
                icon: Icons.payment,
                gradient: CustomerTheme.primaryGradient,
                onPressed: () => context.go('/customer/checkout'),
                width: double.infinity,
                height: CustomerTheme.largeTouchTarget + CustomerTheme.spacing8,
              ),

              const SizedBox(height: CustomerTheme.spacing8),

              // Continue shopping
              TextButton.icon(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  size: 16,
                  color: CustomerTheme.textSecondary,
                ),
                label: Text(
                  'Continue Shopping',
                  style: CustomerTheme.labelMedium.copyWith(
                    color: CustomerTheme.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                fontSize: isTotal ? 18 : 16,
              ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: isTotal ? 18 : 16,
                color: isTotal ? Theme.of(context).colorScheme.primary : null,
              ),
        ),
      ],
    );
  }

  void _showClearCartDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cart'),
        content: const Text(
            'Are you sure you want to remove all items from your cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(cartProvider.notifier).clearCart();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cart cleared'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
