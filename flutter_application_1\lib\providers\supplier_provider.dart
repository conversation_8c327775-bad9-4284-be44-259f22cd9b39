import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';

import '../models/supplier_model.dart';
import '../services/firebase_service.dart';
import 'auth_provider.dart';

// Supplier repository
class SupplierRepository {
  final FirebaseService _firebase = FirebaseService.instance;

  // Get suppliers for a specific merchant and mall
  Stream<List<SupplierModel>> getSuppliers(String merchantId, String mallId) {
    return _firebase.firestore
        .collection('suppliers')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .orderBy('name')
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => SupplierModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Get active suppliers
  Stream<List<SupplierModel>> getActiveSuppliers(String merchantId, String mallId) {
    return _firebase.firestore
        .collection('suppliers')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .where('status', isEqualTo: SupplierStatus.active.value)
        .orderBy('name')
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => SupplierModel.fromFirestore(doc))
              .toList(),
        );
  }

  // Add new supplier
  Future<SupplierModel> addSupplier({
    required String name,
    required String contactPerson,
    required String email,
    required String phone,
    String? alternatePhone,
    required String address,
    String? website,
    required String merchantId,
    required String mallId,
    required String createdBy,
    List<String> productCategories = const [],
    String? taxId,
    String? registrationNumber,
    Map<String, dynamic> paymentTerms = const {"days": 30, "type": "net"},
    double creditLimit = 0.0,
    String? notes,
  }) async {
    final supplierId = const Uuid().v4();
    final now = DateTime.now();

    final supplier = SupplierModel(
      id: supplierId,
      name: name,
      contactPerson: contactPerson,
      email: email,
      phone: phone,
      alternatePhone: alternatePhone,
      address: address,
      website: website,
      merchantId: merchantId,
      mallId: mallId,
      status: SupplierStatus.active,
      productCategories: productCategories,
      taxId: taxId,
      registrationNumber: registrationNumber,
      paymentTerms: paymentTerms,
      creditLimit: creditLimit,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      createdBy: createdBy,
    );

    await _firebase.firestore
        .collection('suppliers')
        .doc(supplierId)
        .set(supplier.toFirestore());

    return supplier;
  }

  // Update supplier
  Future<void> updateSupplier(String supplierId, Map<String, dynamic> updates) async {
    updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
    await _firebase.firestore
        .collection('suppliers')
        .doc(supplierId)
        .update(updates);
  }

  // Update supplier status
  Future<void> updateSupplierStatus(String supplierId, SupplierStatus status) async {
    await _firebase.firestore
        .collection('suppliers')
        .doc(supplierId)
        .update({
          'status': status.value,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });
  }

  // Delete supplier
  Future<void> deleteSupplier(String supplierId) async {
    await _firebase.firestore
        .collection('suppliers')
        .doc(supplierId)
        .delete();
  }

  // Get supplier statistics
  Future<Map<String, dynamic>> getSupplierStatistics(String merchantId, String mallId) async {
    final suppliersSnapshot = await _firebase.firestore
        .collection('suppliers')
        .where('merchantId', isEqualTo: merchantId)
        .where('mallId', isEqualTo: mallId)
        .get();

    final suppliers = suppliersSnapshot.docs
        .map((doc) => SupplierModel.fromFirestore(doc))
        .toList();

    final totalSuppliers = suppliers.length;
    final activeSuppliers = suppliers.where((s) => s.status.isActive).length;
    final inactiveSuppliers = suppliers.where((s) => s.status == SupplierStatus.inactive).length;
    final suspendedSuppliers = suppliers.where((s) => s.status == SupplierStatus.suspended).length;

    final totalCreditLimit = suppliers.fold<double>(0, (sum, supplier) => sum + supplier.creditLimit);
    final totalOutstanding = suppliers.fold<double>(0, (sum, supplier) => sum + supplier.currentBalance);
    final totalPurchases = suppliers.fold<double>(0, (sum, supplier) => sum + supplier.totalPurchases);

    return {
      'totalSuppliers': totalSuppliers,
      'activeSuppliers': activeSuppliers,
      'inactiveSuppliers': inactiveSuppliers,
      'suspendedSuppliers': suspendedSuppliers,
      'totalCreditLimit': totalCreditLimit,
      'totalOutstanding': totalOutstanding,
      'totalPurchases': totalPurchases,
    };
  }
}

// Demo Supplier Repository for testing
class DemoSupplierRepository {
  static final List<SupplierModel> _suppliers = [
    SupplierModel(
      id: '1',
      name: 'TechWorld Distributors',
      contactPerson: 'Ahmed Hassan',
      email: '<EMAIL>',
      phone: '+*********** 4567',
      address: '45 Computer Village, Ikeja, Lagos',
      website: 'www.techworld.com',
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      status: SupplierStatus.active,
      productCategories: ['Electronics', 'Smartphones', 'Accessories'],
      taxId: 'TIN-*********',
      registrationNumber: 'RC-*********',
      paymentTerms: {"days": 30, "type": "net"},
      creditLimit: 5000000.0,
      currentBalance: 1200000.0,
      totalPurchases: 15000000.0,
      totalOrders: 45,
      averageRating: 4.5,
      notes: 'Reliable supplier for electronics',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      createdBy: 'demo-merchant',
    ),
    SupplierModel(
      id: '2',
      name: 'Fresh Foods Limited',
      contactPerson: 'Mary Okafor',
      email: '<EMAIL>',
      phone: '+*********** 5678',
      address: '12 Market Road, Aba, Abia State',
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      status: SupplierStatus.active,
      productCategories: ['Food', 'Beverages', 'Organic'],
      taxId: 'TIN-*********',
      registrationNumber: 'RC-*********',
      paymentTerms: {"days": 15, "type": "net"},
      creditLimit: 2000000.0,
      currentBalance: 450000.0,
      totalPurchases: 8500000.0,
      totalOrders: 32,
      averageRating: 4.8,
      notes: 'Best quality fresh produce',
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      createdBy: 'demo-merchant',
    ),
    SupplierModel(
      id: '3',
      name: 'Fashion Hub Nigeria',
      contactPerson: 'Ibrahim Musa',
      email: '<EMAIL>',
      phone: '+*********** 6789',
      address: '78 Balogun Market, Lagos Island',
      website: 'www.fashionhub.ng',
      merchantId: 'demo-merchant',
      mallId: 'demo-mall',
      status: SupplierStatus.inactive,
      productCategories: ['Fashion', 'Clothing', 'Accessories'],
      taxId: 'TIN-*********',
      registrationNumber: 'RC-*********',
      paymentTerms: {"days": 45, "type": "net"},
      creditLimit: 3000000.0,
      currentBalance: 0.0,
      totalPurchases: 6200000.0,
      totalOrders: 28,
      averageRating: 3.9,
      notes: 'Currently inactive - payment issues',
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      createdBy: 'demo-merchant',
    ),
  ];

  Stream<List<SupplierModel>> getSuppliers(String merchantId, String mallId) {
    return Stream.value(_suppliers
        .where((s) => s.merchantId == merchantId && s.mallId == mallId)
        .toList());
  }

  Stream<List<SupplierModel>> getActiveSuppliers(String merchantId, String mallId) {
    return Stream.value(_suppliers
        .where((s) => s.merchantId == merchantId && 
                     s.mallId == mallId && 
                     s.status.isActive)
        .toList());
  }

  Future<SupplierModel> addSupplier({
    required String name,
    required String contactPerson,
    required String email,
    required String phone,
    String? alternatePhone,
    required String address,
    String? website,
    required String merchantId,
    required String mallId,
    required String createdBy,
    List<String> productCategories = const [],
    String? taxId,
    String? registrationNumber,
    Map<String, dynamic> paymentTerms = const {"days": 30, "type": "net"},
    double creditLimit = 0.0,
    String? notes,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500));

    final supplier = SupplierModel(
      id: const Uuid().v4(),
      name: name,
      contactPerson: contactPerson,
      email: email,
      phone: phone,
      alternatePhone: alternatePhone,
      address: address,
      website: website,
      merchantId: merchantId,
      mallId: mallId,
      status: SupplierStatus.active,
      productCategories: productCategories,
      taxId: taxId,
      registrationNumber: registrationNumber,
      paymentTerms: paymentTerms,
      creditLimit: creditLimit,
      notes: notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: createdBy,
    );

    _suppliers.add(supplier);
    return supplier;
  }

  Future<void> updateSupplier(String supplierId, Map<String, dynamic> updates) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final index = _suppliers.indexWhere((s) => s.id == supplierId);
    if (index != -1) {
      // Apply updates to the supplier
      final supplier = _suppliers[index];
      _suppliers[index] = supplier.copyWith(
        name: updates['name'] ?? supplier.name,
        contactPerson: updates['contactPerson'] ?? supplier.contactPerson,
        email: updates['email'] ?? supplier.email,
        phone: updates['phone'] ?? supplier.phone,
        alternatePhone: updates['alternatePhone'] ?? supplier.alternatePhone,
        address: updates['address'] ?? supplier.address,
        website: updates['website'] ?? supplier.website,
        productCategories: updates['productCategories'] ?? supplier.productCategories,
        taxId: updates['taxId'] ?? supplier.taxId,
        registrationNumber: updates['registrationNumber'] ?? supplier.registrationNumber,
        paymentTerms: updates['paymentTerms'] ?? supplier.paymentTerms,
        creditLimit: updates['creditLimit'] ?? supplier.creditLimit,
        notes: updates['notes'] ?? supplier.notes,
        updatedAt: DateTime.now(),
      );
    }
  }

  Future<void> updateSupplierStatus(String supplierId, SupplierStatus status) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final index = _suppliers.indexWhere((s) => s.id == supplierId);
    if (index != -1) {
      _suppliers[index] = _suppliers[index].copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );
    }
  }

  Future<void> deleteSupplier(String supplierId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    _suppliers.removeWhere((s) => s.id == supplierId);
  }
}

// Providers
final supplierRepositoryProvider = Provider<SupplierRepository>((ref) => SupplierRepository());
final demoSupplierRepositoryProvider = Provider<DemoSupplierRepository>((ref) => DemoSupplierRepository());

// Current user's suppliers provider
final currentUserSuppliersProvider = StreamProvider<List<SupplierModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(demoSupplierRepositoryProvider); // Use demo for now

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return repository.getSuppliers(userData!.id, userData.mallId!);
      }
      return Stream.value(<SupplierModel>[]);
    },
    loading: () => Stream.value(<SupplierModel>[]),
    error: (_, __) => Stream.value(<SupplierModel>[]),
  );
});

// Active suppliers provider
final activeSuppliersProvider = StreamProvider<List<SupplierModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final repository = ref.watch(demoSupplierRepositoryProvider);

  return user.when(
    data: (userData) {
      if (userData?.role.isMerchant == true && userData?.mallId != null) {
        return repository.getActiveSuppliers(userData!.id, userData.mallId!);
      }
      return Stream.value(<SupplierModel>[]);
    },
    loading: () => Stream.value(<SupplierModel>[]),
    error: (_, __) => Stream.value(<SupplierModel>[]),
  );
});

// Supplier management notifier
class SupplierNotifier extends StateNotifier<AsyncValue<SupplierModel?>> {
  SupplierNotifier(this._repository) : super(const AsyncValue.data(null));

  final DemoSupplierRepository _repository;

  Future<SupplierModel?> addSupplier({
    required String name,
    required String contactPerson,
    required String email,
    required String phone,
    String? alternatePhone,
    required String address,
    String? website,
    required String merchantId,
    required String mallId,
    required String createdBy,
    List<String> productCategories = const [],
    String? taxId,
    String? registrationNumber,
    Map<String, dynamic> paymentTerms = const {"days": 30, "type": "net"},
    double creditLimit = 0.0,
    String? notes,
  }) async {
    state = const AsyncValue.loading();
    try {
      final supplier = await _repository.addSupplier(
        name: name,
        contactPerson: contactPerson,
        email: email,
        phone: phone,
        alternatePhone: alternatePhone,
        address: address,
        website: website,
        merchantId: merchantId,
        mallId: mallId,
        createdBy: createdBy,
        productCategories: productCategories,
        taxId: taxId,
        registrationNumber: registrationNumber,
        paymentTerms: paymentTerms,
        creditLimit: creditLimit,
        notes: notes,
      );
      state = AsyncValue.data(supplier);
      return supplier;
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
      return null;
    }
  }

  Future<void> updateSupplier(String supplierId, Map<String, dynamic> updates) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateSupplier(supplierId, updates);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> updateSupplierStatus(String supplierId, SupplierStatus status) async {
    state = const AsyncValue.loading();
    try {
      await _repository.updateSupplierStatus(supplierId, status);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> deleteSupplier(String supplierId) async {
    state = const AsyncValue.loading();
    try {
      await _repository.deleteSupplier(supplierId);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

final supplierNotifierProvider = StateNotifierProvider<SupplierNotifier, AsyncValue<SupplierModel?>>((ref) {
  final repository = ref.watch(demoSupplierRepositoryProvider);
  return SupplierNotifier(repository);
});
