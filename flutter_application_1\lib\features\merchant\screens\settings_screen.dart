import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../providers/settings_provider.dart';
import '../../../shared/themes/merchant_theme.dart';

/// Professional Settings & Configuration Screen
/// Features: Business settings, notifications, inventory config,
/// order settings, reports, and user preferences
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen>
    with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  
  late AnimationController _pageController;
  late Animation<double> _pageAnimation;
  
  final List<Map<String, dynamic>> _tabs = [
    {'title': 'Business Info', 'icon': Icons.business},
    {'title': 'Notifications', 'icon': Icons.notifications},
    {'title': 'Inventory', 'icon': Icons.inventory_2},
    {'title': 'Security', 'icon': Icons.security},
  ];

  @override
  void initState() {
    super.initState();
    _pageController = AnimationController(
      duration: MerchantTheme.slowAnimation,
      vsync: this,
    );
    _pageAnimation = CurvedAnimation(
      parent: _pageController,
      curve: Curves.easeOutCubic,
    );
    _pageController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(currentUserSettingsProvider);
    final padding = MerchantTheme.getResponsivePadding(context);
    final isWide = MediaQuery.of(context).size.width > 900;

    return Theme(
      data: MerchantTheme.lightTheme,
      child: Scaffold(
        backgroundColor: MerchantTheme.neutral50,
        body: Row(
          children: [
            // Sidebar Navigation
            Container(
              width: isWide ? 260 : 80,
              color: Colors.white,
              child: _buildSidebarNavigation(isWide),
            ),
            // Main Content
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(padding * 2),
                child: settingsAsync.when(
                  data: (settings) => _buildSettingsContent(settings),
                  loading: () => _buildLoadingSection(),
                  error: (error, stack) => _buildErrorSection(error.toString()),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSidebarNavigation(bool isWide) {
    return Column(
      children: [
        Container(
          height: 100,
          alignment: Alignment.center,
          child: const Icon(Icons.settings, size: 40, color: MerchantTheme.primaryBlue),
        ),
        ..._tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = _selectedTabIndex == index;
          return Material(
            color: isSelected ? MerchantTheme.primaryBlue.withOpacity(0.08) : Colors.transparent,
            child: InkWell(
              onTap: () => setState(() => _selectedTabIndex = index),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 18, horizontal: isWide ? 24 : 0),
                child: Row(
                  children: [
                    Icon(tab['icon'], color: isSelected ? MerchantTheme.primaryBlue : MerchantTheme.neutral400, size: 22),
                    if (isWide) ...[
                      const SizedBox(width: 16),
                      Text(
                        tab['title'],
                        style: MerchantTheme.bodyLarge.copyWith(
                          color: isSelected ? MerchantTheme.primaryBlue : MerchantTheme.neutral500,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        }),
        const Spacer(),
      ],
    );
  }

  Widget _buildSettingsContent(dynamic settings) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildBusinessInfoTab(settings);
      case 1:
        return _buildNotificationsTab(settings);
      case 2:
        return _buildInventoryTab(settings);
      case 3:
        return _buildSecurityTab(settings);
      default:
        return _buildBusinessInfoTab(settings);
    }
  }

  Widget _buildBusinessInfoTab(dynamic settings) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Business Information', Icons.business),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildTextField('Business Name', 'Enter your business name', 'Mall Store'),
            _buildTextField('Business Address', 'Enter your business address', '123 Main Street, Lagos'),
            _buildTextField('Phone Number', 'Enter your phone number', '+234 ************'),
            _buildTextField('Email Address', 'Enter your email address', '<EMAIL>'),
            _buildTextField('Website', 'Enter your website URL', 'https://www.example.com'),
            _buildTextField('Tax ID', 'Enter your tax identification number', 'TAX123456789'),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Business Hours', Icons.access_time),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildTimeRangeField('Monday - Friday', '09:00', '18:00'),
            _buildTimeRangeField('Saturday', '10:00', '16:00'),
            _buildTimeRangeField('Sunday', 'Closed', 'Closed'),
          ]),
        ],
      ),
    );
  }

  Widget _buildNotificationsTab(dynamic settings) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Notification Preferences', Icons.notifications),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildSwitchTile('Order Notifications', 'Get notified when new orders are received', true),
            _buildSwitchTile('Low Stock Alerts', 'Get notified when products are running low', true),
            _buildSwitchTile('Payment Notifications', 'Get notified when payments are received', true),
            _buildSwitchTile('Customer Messages', 'Get notified when customers send messages', false),
            _buildSwitchTile('System Updates', 'Get notified about system updates and maintenance', true),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Email Notifications', Icons.email),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildSwitchTile('Daily Sales Report', 'Receive daily sales summary via email', true),
            _buildSwitchTile('Weekly Analytics', 'Receive weekly analytics report via email', true),
            _buildSwitchTile('Monthly Reports', 'Receive monthly business reports via email', true),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('SMS Notifications', Icons.sms),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildSwitchTile('Order Confirmations', 'Send SMS confirmations to customers', true),
            _buildSwitchTile('Delivery Updates', 'Send SMS updates about order delivery', true),
            _buildSwitchTile('Promotional Messages', 'Send promotional SMS to customers', false),
          ]),
        ],
      ),
    );
  }

  Widget _buildInventoryTab(dynamic settings) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Inventory Management', Icons.inventory_2),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildNumberField('Low Stock Threshold', 'Minimum stock level before alert', '10'),
            _buildNumberField('Reorder Point', 'Stock level to trigger reorder', '5'),
            _buildSwitchTile('Auto Reorder', 'Automatically reorder products when stock is low', false),
            _buildSwitchTile('Stock Tracking', 'Track stock movements and history', true),
            _buildSwitchTile('Barcode Scanning', 'Enable barcode scanning for products', true),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Product Categories', Icons.category),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildTextField('Default Category', 'Default category for new products', 'General'),
            _buildSwitchTile('Category Management', 'Allow creating and editing categories', true),
            _buildSwitchTile('Subcategories', 'Enable subcategory support', false),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Pricing', Icons.attach_money),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildTextField('Currency', 'Default currency for pricing', '₦ (Naira)'),
            _buildSwitchTile('Tax Calculation', 'Automatically calculate tax on orders', true),
            _buildNumberField('Tax Rate (%)', 'Default tax rate percentage', '7.5'),
            _buildSwitchTile('Discount Management', 'Allow setting product discounts', true),
          ]),
        ],
      ),
    );
  }

  Widget _buildSecurityTab(dynamic settings) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Account Security', Icons.security),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildButtonTile('Change Password', 'Update your account password', Icons.lock, _changePassword),
            _buildButtonTile('Two-Factor Authentication', 'Enable 2FA for enhanced security', Icons.verified_user, _enableTwoFactor),
            _buildSwitchTile('Login Notifications', 'Get notified of new login attempts', true),
            _buildSwitchTile('Session Management', 'Manage active sessions', true),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Data Privacy', Icons.privacy_tip),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildSwitchTile('Data Collection', 'Allow data collection for analytics', true),
            _buildSwitchTile('Third-Party Sharing', 'Share data with third-party services', false),
            _buildSwitchTile('Customer Data Retention', 'Retain customer data for analysis', true),
            _buildNumberField('Data Retention (days)', 'Days to retain customer data', '365'),
          ]),
          
          const SizedBox(height: MerchantTheme.spacing32),
          _buildSectionHeader('Backup & Recovery', Icons.backup),
          const SizedBox(height: MerchantTheme.spacing16),
          _buildSettingsCard([
            _buildSwitchTile('Auto Backup', 'Automatically backup data', true),
            _buildSwitchTile('Cloud Backup', 'Backup data to cloud storage', true),
            _buildButtonTile('Manual Backup', 'Create a manual backup now', Icons.cloud_upload, _createBackup),
            _buildButtonTile('Restore Data', 'Restore data from backup', Icons.restore, _restoreData),
          ]),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: MerchantTheme.primaryBlue, size: 24),
        const SizedBox(width: MerchantTheme.spacing12),
        Text(
          title,
          style: MerchantTheme.headline4.copyWith(
            color: MerchantTheme.neutral800,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
        boxShadow: MerchantTheme.cardShadow,
      ),
      child: Column(
        children: children.map((child) => Column(
          children: [
            child,
            if (child != children.last)
              const Divider(height: 1, indent: 16, endIndent: 16),
          ],
        )).toList(),
      ),
    );
  }

  Widget _buildTextField(String label, String hint, String defaultValue) {
    return Padding(
      padding: const EdgeInsets.all(MerchantTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: MerchantTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: MerchantTheme.neutral700,
            ),
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          TextField(
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: MerchantTheme.spacing16,
                vertical: MerchantTheme.spacing12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberField(String label, String hint, String defaultValue) {
    return Padding(
      padding: const EdgeInsets.all(MerchantTheme.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: MerchantTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: MerchantTheme.neutral700,
            ),
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          TextField(
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: hint,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: MerchantTheme.spacing16,
                vertical: MerchantTheme.spacing12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeField(String label, String startTime, String endTime) {
    return Padding(
      padding: const EdgeInsets.all(MerchantTheme.spacing16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: MerchantTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: MerchantTheme.neutral700,
                  ),
                ),
                const SizedBox(height: MerchantTheme.spacing8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: startTime,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: MerchantTheme.spacing12,
                            vertical: MerchantTheme.spacing8,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: MerchantTheme.spacing8),
                    const Text('to', style: MerchantTheme.bodyMedium),
                    const SizedBox(width: MerchantTheme.spacing8),
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: endTime,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(MerchantTheme.radiusMedium),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: MerchantTheme.spacing12,
                            vertical: MerchantTheme.spacing8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile(String title, String subtitle, bool defaultValue) {
    return SwitchListTile(
      title: Text(
        title,
        style: MerchantTheme.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          color: MerchantTheme.neutral700,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: MerchantTheme.bodySmall.copyWith(
          color: MerchantTheme.neutral500,
        ),
      ),
      value: defaultValue,
      onChanged: (value) {
        // TODO: Implement switch logic
      },
      activeColor: MerchantTheme.primaryBlue,
    );
  }

  Widget _buildButtonTile(String title, String subtitle, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: MerchantTheme.primaryBlue),
      title: Text(
        title,
        style: MerchantTheme.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          color: MerchantTheme.neutral700,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: MerchantTheme.bodySmall.copyWith(
          color: MerchantTheme.neutral500,
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildLoadingSection() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorSection(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: MerchantTheme.errorRed),
          const SizedBox(height: MerchantTheme.spacing16),
          Text(
            'Error loading settings',
            style: MerchantTheme.headline3.copyWith(color: MerchantTheme.errorRed),
          ),
          const SizedBox(height: MerchantTheme.spacing8),
          Text(error, style: MerchantTheme.bodyMedium, textAlign: TextAlign.center),
          const SizedBox(height: MerchantTheme.spacing24),
          ElevatedButton(
            onPressed: () => ref.invalidate(currentUserSettingsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Action Methods
  void _saveAllSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings saved successfully!'),
        backgroundColor: MerchantTheme.successGreen,
      ),
    );
  }

  void _changePassword() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change Password - Coming Soon!'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _enableTwoFactor() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Two-Factor Authentication - Coming Soon!'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _createBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Creating backup...'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }

  void _restoreData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Restore Data - Coming Soon!'),
        backgroundColor: MerchantTheme.primaryBlue,
      ),
    );
  }
} 