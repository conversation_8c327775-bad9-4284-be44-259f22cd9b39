import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mall_management_system/providers/demo_auth_provider.dart';
import 'package:mall_management_system/providers/role_based_auth_provider.dart';

void main() {
  group('Logout Functionality Tests', () {
    test('Demo user provider logout method works', () {
      final container = ProviderContainer();
      
      // Login demo user
      container.read(demoUserProvider.notifier).loginDemoUser('<EMAIL>');
      
      // Verify user is logged in
      var user = container.read(demoUserProvider);
      expect(user, isNotNull);
      expect(user?.email, '<EMAIL>');
      
      // Logout
      container.read(demoUserProvider.notifier).logout();
      
      // Verify user is logged out
      user = container.read(demoUserProvider);
      expect(user, isNull);
      
      container.dispose();
    });

    test('Role-based auth provider signOut method works', () async {
      final container = ProviderContainer();
      
      // Login demo user first
      container.read(demoUserProvider.notifier).loginDemoUser('<EMAIL>');
      
      // Verify user is logged in
      var user = container.read(unifiedCurrentUserProvider);
      expect(user, isNotNull);
      expect(user?.email, '<EMAIL>');
      
      // Sign out using role-based auth provider
      await container.read(roleBasedAuthProvider.notifier).signOut();
      
      // Verify user is logged out
      user = container.read(unifiedCurrentUserProvider);
      expect(user, isNull);
      
      // Verify authentication state
      final isAuthenticated = container.read(isAuthenticatedUnifiedProvider);
      expect(isAuthenticated, false);
      
      container.dispose();
    });

    test('Unified authentication providers work correctly', () {
      final container = ProviderContainer();
      
      // Initially no user should be authenticated
      var isAuthenticated = container.read(isAuthenticatedUnifiedProvider);
      expect(isAuthenticated, false);
      
      var user = container.read(unifiedCurrentUserProvider);
      expect(user, isNull);
      
      // Login demo user
      container.read(demoUserProvider.notifier).loginDemoUser('<EMAIL>');
      
      // Verify user is authenticated
      isAuthenticated = container.read(isAuthenticatedUnifiedProvider);
      expect(isAuthenticated, true);
      
      user = container.read(unifiedCurrentUserProvider);
      expect(user, isNotNull);
      expect(user?.email, '<EMAIL>');
      
      // Logout
      container.read(demoUserProvider.notifier).logout();
      
      // Verify user is logged out
      isAuthenticated = container.read(isAuthenticatedUnifiedProvider);
      expect(isAuthenticated, false);
      
      user = container.read(unifiedCurrentUserProvider);
      expect(user, isNull);
      
      container.dispose();
    });
  });
}
